from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import rag, documents

app = FastAPI(
    title="RAG API",
    description="A REST API for Retrieval-Augmented Generation with Bangla text support",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(rag.router, prefix="/api/v1", tags=["rag"])
app.include_router(documents.router, prefix="/api/v1", tags=["documents"])

@app.get("/")
def root():
    return {"message": "RAG API is running!", "version": "1.0.0"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "RAG API"}


