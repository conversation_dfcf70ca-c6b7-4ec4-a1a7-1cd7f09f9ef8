import os
from langchain_google_genai import GoogleGenerativeAIEmbeddings, GoogleGenerativeAI
from langchain_community.vectorstores import Chroma
from langchain.prompts import ChatPromptTemplate
from langchain_community.document_loaders import UnstructuredMarkdownLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser
from typing import List, Dict, Any

class RAGService:
    def __init__(self):
        self.embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        self.llm = GoogleGenerativeAI(model="models/gemini-2.5-pro")
        self.vectorstore = None
        self.retriever = None
        self._initialize_vectorstore()
        
    def _initialize_vectorstore(self):
        """Initialize the vector store with documents"""
        try:
            # Load data
            loader = UnstructuredMarkdownLoader("notebooks/extracted_folder/parsed_text.markdown")
            doc = loader.load()
            
            if not doc:
                raise ValueError("No documents loaded from markdown file")
            
            # Split documents
            markdown_splitter = RecursiveCharacterTextSplitter(
                chunk_size=8000,  
                chunk_overlap=1000,
                separators=["\n\n", "\n", ".", "।", " ", "", '#', '##', '###', '####', '#####', '######']
            )
            splits = markdown_splitter.split_documents(doc)
            
            # Create vector store
            self.vectorstore = Chroma.from_documents(
                documents=splits,
                embedding=self.embeddings
            )
            self.retriever = self.vectorstore.as_retriever(search_kwargs={"k": 20})
        
        except Exception as e:
            print(f"Error initializing vectorstore: {e}")
            self.vectorstore = None
            self.retriever = None
    
    def _format_docs(self, docs) -> str:
        """Format documents for context"""
        return "\n\n".join(doc.page_content for doc in docs)
    
    def query(self, question: str) -> Dict[str, Any]:
        """Basic RAG query"""
        if self.retriever is None:
            raise ValueError("Retriever not initialized. Check if the markdown file exists and vectorstore initialization succeeded.")
        
        prompt = ChatPromptTemplate.from_template(
            """Answer in bangla. Answer (answer only, no extra text) the user's question based on the context provided.
            Context: {context}
            Question: {question}
            Answer:"""
        )
        
        rag_chain = (
            {"context": self.retriever | self._format_docs, "question": RunnablePassthrough()}
            | prompt
            | self.llm
            | StrOutputParser()
        )
        
        answer = rag_chain.invoke(question)
        docs = self.retriever.invoke(question)
        sources = [doc.page_content[:100] + "..." for doc in docs[:3]]
        
        return {"answer": answer, "sources": sources}





