from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List
from ..services.rag_service import RAGService

router = APIRouter()

# Initialize RAG service
rag_service = RAGService()

class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    answer: str
    sources: List[str] = []

@router.post("/query", response_model=QueryResponse)
def query_rag(request: QueryRequest):
    """Query the RAG system with a question"""
    try:
        result = rag_service.query(question=request.question)
        return QueryResponse(
            answer=result["answer"],
            sources=result["sources"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@router.get("/health")
def rag_health():
    """Check RAG service health"""
    return {"status": "RAG service is healthy"}



