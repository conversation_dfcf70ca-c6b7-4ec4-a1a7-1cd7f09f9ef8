from dotenv import load_dotenv
load_dotenv('../.env', override=True)


import re, uuid, unicodedata
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter


class BanglaTextPreprocessor:
    def __init__(self):
        self.keep_chars = re.compile(r'[^\u0980-\u09FF\s.,!?“”"\'():;।-]')
        self.repeated_punct = re.compile(r'([।,!?:;])\1+')

    def normalize_unicode(self, text):
        return unicodedata.normalize("NFC", text)

    def clean(self, text: str) -> str:
        text = self.normalize_unicode(text)
        text = text.replace('\n', ' ').replace('\u200c', '').replace('\u200d', '')
        text = self.keep_chars.sub('', text)
        text = self.repeated_punct.sub(r'\1', text)
        text = re.sub(r' +', ' ', text)
        text = re.sub(r'\s([.,!?;।])', r'\1', text)
        return text.strip()


with open("extracted_folder/parsed_text.markdown", "r", encoding="utf-8") as f:
    raw_text = f.read()

print("Text sample preview:\n", raw_text[:500])



# Updated pattern for Bangla MCQs like "১। প্রশ্ন ..."
pattern = r"(?:^|\n)([০-৯]+।.*?)(?=(\n[০-৯]+।|$))"

matches = re.findall(pattern, raw_text, re.DOTALL)
print(f"Total level-1 MCQ-style sections found: {len(matches)}")



pre = BanglaTextPreprocessor()
rcs = RecursiveCharacterTextSplitter(
    chunk_size=400,
    chunk_overlap=80,
    separators=["\n\n", "\n", ".", "।", " ", ""]
)

raptor_chunks = []

# Pull just the matched blocks
blocks = [m[0] for m in matches]

for i, block in enumerate(blocks):
    section_text = pre.clean(block)
    parent_id = str(uuid.uuid4())

    # Level 1
    raptor_chunks.append(Document(
        page_content=section_text,
        metadata={
            "chunk_level": 1,
            "chunk_id": parent_id,
            "section": f"question_{i+1}"
        }
    ))

    # Level 2
    children = rcs.create_documents([section_text])
    for j, child in enumerate(children):
        raptor_chunks.append(Document(
            page_content=child.page_content,
            metadata={
                "chunk_level": 2,
                "chunk_id": f"{parent_id}_{j}",
                "parent_id": parent_id,
                "section": f"question_{i+1}"
            }
        ))


print(f"✅ Total chunks after RAPTOR-style processing: {len(raptor_chunks)}")
print("--- Sample chunk ---")
print("Level:", raptor_chunks[0].metadata.get("chunk_level"))
print("Metadata:", raptor_chunks[0].metadata)
print("Text:\n", raptor_chunks[0].page_content)


from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import Chroma
#### Embedd RAPTOR Chunks

texts = [doc.page_content for doc in raptor_chunks]
metadatas = [doc.metadata for doc in raptor_chunks]

embeddings_model = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
vectorstore = Chroma.from_texts(texts, embeddings_model, metadatas=metadatas)
print(f"Vector store created with {len(raptor_chunks)} documents.")

# Retrieve
retriever = vectorstore.as_retriever(search_kwargs={"k": 20})


# Prompt

from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_google_genai import ChatGoogleGenerativeAI


# Multi Query: Different Perspectives
template = """You are an AI language model assistant. Your task is to generate exactly five (5) 
different versions of the given user question to retrieve relevant documents from a vector 
database. By generating multiple perspectives on the user question, your goal is to help
the user overcome some of the limitations of the distance-based similarity search. 
Provide these alternative questions separated by newlines. Original question: {question}"""
prompt_perspectives = ChatPromptTemplate.from_template(template)


generate_queries = (
    prompt_perspectives
    | ChatGoogleGenerativeAI(model="gemini-2.5-pro")
    | StrOutputParser()
    | (lambda x: x.split("\n"))
)


# Filter documents retrieval
from langchain.load import dumps, loads

def get_unique_union(documents: list[list]):
    """ Unique union of retrieved docs """
    # Flatten list of lists, and convert each Document to string
    flattened_docs = [dumps(doc) for sublist in documents for doc in sublist]
    # Get unique documents
    unique_docs = list(set(flattened_docs))
    # Return
    return [loads(doc) for doc in unique_docs]

# Retrieve
question = "বিয়ের সময় কল্যাণীর প্রকৃত বয়স কত ছিল?"
retrieval_chain = generate_queries | retriever.map() | get_unique_union
docs = retrieval_chain.invoke({"question":question})
len(docs)


docs[:4]

# Generation

from operator import itemgetter

# Final layer of involving LLM
template = """Answer (only the answer, no extra other text) the following question based on this context, if not found answer that you didn't find the answer:

{context}

Question: {question}
"""

prompt = ChatPromptTemplate.from_template(template)

llm = ChatGoogleGenerativeAI(model="gemini-2.5-pro")

final_rag_chain = (
    {"context": retrieval_chain, 
     "question": itemgetter("question")} 
    | prompt
    | llm
    | StrOutputParser()
)

final_rag_chain.invoke({"question":question})



# Generation
from langchain.prompts import ChatPromptTemplate
from langchain_google_genai import GoogleGenerativeAI
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser


llm = GoogleGenerativeAI(model="models/gemini-2.5-pro")

prompt = ChatPromptTemplate.from_template(
    """Answer the user's question based on the context provided.
    Context: {context}
    Question: {question}
    Answer:"""
)

# Chain
rag_chain = (
    {"context": retriever, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)

# Question
rag_chain.invoke("অনুপমের ভাষায় সুপুরুষ কাকে বলা হয়েছে?")


# Question
rag_chain.invoke("কাকে অনুপমের ভাগ্য দেবতা বলে উল্লেখ করা হয়েছে?")

