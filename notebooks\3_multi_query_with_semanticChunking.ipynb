from dotenv import load_dotenv
load_dotenv('../.env', override=True)


# Load data
from langchain.document_loaders import UnstructuredMarkdownLoader
loader = UnstructuredMarkdownLoader("extracted_folder/parsed_text.markdown")
doc = loader.load()
len(doc)

doc

# Chunking the markdown doc
from langchain_experimental.text_splitter import SemanticChunker
from langchain_google_genai import GoogleGenerativeAIEmbeddings

text_splitter = SemanticChunker(embeddings=GoogleGenerativeAIEmbeddings(model="models/embedding-001"), breakpoint_threshold_type='percentile')
splits = text_splitter.create_documents([d.page_content for d in doc])
len(splits)

splits[2]

# Index
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import Chroma

vectorstore = Chroma.from_documents(
    documents=splits,
    embedding=GoogleGenerativeAIEmbeddings(model="models/embedding-001")
)

retriever = vectorstore.as_retriever()

# Prompt

from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_google_genai import ChatGoogleGenerativeAI


# Multi Query: Different Perspectives
template = """You are an AI language model assistant. Your task is to generate exactly five (5) 
different versions of the given user question to retrieve relevant documents from a vector 
database. By generating multiple perspectives on the user question, your goal is to help
the user overcome some of the limitations of the distance-based similarity search. 
Provide these alternative questions separated by newlines. Original question: {question}"""
prompt_perspectives = ChatPromptTemplate.from_template(template)


generate_queries = (
    prompt_perspectives
    | ChatGoogleGenerativeAI(model="gemini-2.5-pro")
    | StrOutputParser()
    | (lambda x: x.split("\n"))
)


# Filter documents retrieval
from langchain.load import dumps, loads

def get_unique_union(documents: list[list]):
    """ Unique union of retrieved docs """
    # Flatten list of lists, and convert each Document to string
    flattened_docs = [dumps(doc) for sublist in documents for doc in sublist]
    # Get unique documents
    unique_docs = list(set(flattened_docs))
    # Return
    return [loads(doc) for doc in unique_docs]

# Retrieve
question = "বিয়ের সময় কল্যাণীর প্রকৃত বয়স কত ছিল?"
retrieval_chain = generate_queries | retriever.map() | get_unique_union
docs = retrieval_chain.invoke({"question":question})
len(docs)


docs[:4]

# Generation

from operator import itemgetter

# Final layer of involving LLM
template = """Answer (only the answer, no extra other text) the following question based on this context, if not found answer that you didn't find the answer:

{context}

Question: {question}
"""

prompt = ChatPromptTemplate.from_template(template)

llm = ChatGoogleGenerativeAI(model="gemini-2.5-pro")

final_rag_chain = (
    {"context": retrieval_chain, 
     "question": itemgetter("question")} 
    | prompt
    | llm
    | StrOutputParser()
)

final_rag_chain.invoke({"question":question})



# Generation
from langchain.prompts import ChatPromptTemplate
from langchain_google_genai import GoogleGenerativeAI
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser


llm = GoogleGenerativeAI(model="models/gemini-2.5-pro")

prompt = ChatPromptTemplate.from_template(
    """Answer the user's question based on the context provided.
    Context: {context}
    Question: {question}
    Answer:"""
)

# Post-processing
def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)

# Chain
rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)

# Question
rag_chain.invoke("অনুপমের ভাষায় সুপুরুষ কাকে বলা হয়েছে?")


